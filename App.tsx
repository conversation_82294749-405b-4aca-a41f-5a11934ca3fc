
import React, { useRef } from 'react';
import PatientIdCard from './components/PatientIdCard';
import { mockPatientData } from './constants';

// Make sure jsPDF and html2canvas are available globally from CDN
declare var jsPDF: any;
declare var html2canvas: any;


const App: React.FC = () => {
  const cardRef = useRef<HTMLDivElement>(null);

  const handleDownloadPdf = async () => {
    if (!cardRef.current) {
      console.error("Card element not found");
      return;
    }
    if (typeof html2canvas === 'undefined' || typeof jsPDF === 'undefined') {
      alert('PDF generation libraries (html2canvas, jsPDF) are not loaded. Check CDN links.');
      return;
    }

    try {
      const canvas = await html2canvas(cardRef.current, {
        scale: 2, // Increase scale for better quality
        useCORS: true, // If images are from external sources
      });
      const imgData = canvas.toDataURL('image/png');
      
      // A4 dimensions: 210mm x 297mm. Card is ~85.6mm x 53.98mm (credit card size)
      // Let's make PDF page slightly larger than card to have margins or fit well.
      // The card in the image is portrait.
      // Let's assume the card is roughly 90mm wide, 150mm tall (visual estimate from image)
      // For jsPDF, units are in mm by default for new jsPDF('p', 'mm', 'a4')
      // Let's try to make the PDF page match the image aspect ratio
      const cardWidthPx = cardRef.current.offsetWidth;
      const cardHeightPx = cardRef.current.offsetHeight;
      
      // Convert px to mm (approx @ 96 DPI: 1 inch = 25.4 mm, 1 inch = 96 px => 1px = 0.26458333 mm)
      const pxToMm = 0.26458333;
      const pdfCardWidthMm = cardWidthPx * pxToMm;
      const pdfCardHeightMm = cardHeightPx * pxToMm;

      // Use jsPDF with 'p' for portrait, 'mm' for millimeters, and custom dimensions
      const pdf = new jsPDF({
        orientation: pdfCardWidthMm > pdfCardHeightMm ? 'l' : 'p',
        unit: 'mm',
        format: [pdfCardWidthMm, pdfCardHeightMm]
      });

      pdf.addImage(imgData, 'PNG', 0, 0, pdfCardWidthMm, pdfCardHeightMm);
      pdf.save('patient_id_card.pdf');
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. See console for details.');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-100 to-sky-100 flex flex-col items-center justify-center p-4 selection:bg-orange-200 selection:text-orange-800">
      <div className="mb-8 text-center">
        <h1 className="text-4xl font-bold text-blue-700">Patient ID Card Preview</h1>
        <p className="text-slate-600 mt-2">Review the card details below. You can download it as a PDF.</p>
      </div>
      
      <div id="patientCard" ref={cardRef}>
        <PatientIdCard patientData={mockPatientData} />
      </div>

      <button
        onClick={handleDownloadPdf}
        className="mt-8 px-8 py-3 bg-green-600 hover:bg-green-700 text-white font-semibold rounded-lg shadow-md transition-transform transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
      >
        Download PDF
      </button>
      <footer className="mt-12 text-center text-sm text-slate-500">
        <p>&copy; {new Date().getFullYear()} Bhaktivedanta Hospital & Research Institute. All rights reserved.</p>
        <p>This is a sample application for demonstration purposes.</p>
      </footer>
    </div>
  );
};

export default App;
