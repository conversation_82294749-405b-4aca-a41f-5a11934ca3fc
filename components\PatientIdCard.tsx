import React from 'react';
import { PatientData } from '../types';

interface PatientIdCardProps {
  patientData: PatientData;
}

const PatientIdCard: React.FC<PatientIdCardProps> = ({ patientData }) => {
  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden w-96 h-64 relative border-2 border-blue-200">
      {/* Header with Hospital Name */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-3 text-center">
        <h2 className="text-lg font-bold">{patientData.hospitalName}</h2>
        <p className="text-xs opacity-90">{patientData.hospitalSubtitle}</p>
      </div>

      {/* Main Content */}
      <div className="p-4 flex gap-4 h-full">
        {/* Left Side - Patient Photo */}
        <div className="flex-shrink-0">
          <div className="w-20 h-24 bg-gray-200 rounded border overflow-hidden">
            <img 
              src={patientData.photoUrl} 
              alt="Patient Photo"
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEwMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjNGNEY2Ii8+CjxjaXJjbGUgY3g9IjUwIiBjeT0iNDAiIHI9IjE1IiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik0yNSA4NUMyNSA3NS4zMzUgMzMuMzM1IDY3IDQzIDY3SDU3QzY2LjY2NSA2NyA3NSA3NS4zMzUgNzUgODVWOTVIMjVWODVaIiBmaWxsPSIjOUNBM0FGIi8+Cjx0ZXh0IHg9IjUwIiB5PSIxMTAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiM2QjcyODAiIGZvbnQtc2l6ZT0iOCIgZm9udC1mYW1pbHk9IkFyaWFsIj5ObyBQaG90bzwvdGV4dD4KPC9zdmc+';
              }}
            />
          </div>
        </div>

        {/* Right Side - Patient Information */}
        <div className="flex-1 space-y-2">
          <div>
            <p className="text-xs text-gray-600 font-medium">MRN/BH ID:</p>
            <p className="text-sm font-bold text-blue-800">{patientData.mrnBhId}</p>
          </div>
          
          <div>
            <p className="text-xs text-gray-600 font-medium">Patient Name:</p>
            <p className="text-sm font-bold">{patientData.name}</p>
          </div>
          
          <div>
            <p className="text-xs text-gray-600 font-medium">Age:</p>
            <p className="text-sm">{patientData.age}</p>
          </div>
          
          <div>
            <p className="text-xs text-gray-600 font-medium">Mobile:</p>
            <p className="text-sm">{patientData.registeredMobileNumber}</p>
          </div>
        </div>
      </div>

      {/* Footer with Contact Information */}
      <div className="absolute bottom-0 left-0 right-0 bg-gray-50 p-2 border-t">
        <div className="flex justify-between items-center text-xs">
          <div>
            <p className="text-gray-600">Website: <span className="text-blue-600">{patientData.websiteUrl}</span></p>
            <p className="text-gray-600">Appointment: {patientData.appointmentPhoneNumber}</p>
          </div>
          <div className="text-right">
            <p className="text-gray-600">WhatsApp: {patientData.whatsappBhaktiNumber}</p>
            {/* Simple barcode representation */}
            <div className="mt-1 flex gap-px">
              {patientData.barcodeValue.split('').slice(0, 15).map((digit, index) => (
                <div 
                  key={index} 
                  className="bg-black" 
                  style={{ 
                    width: '2px', 
                    height: digit === '1' ? '12px' : '8px' 
                  }}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientIdCard;
