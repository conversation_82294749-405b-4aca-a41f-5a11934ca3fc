import React from 'react';
import { PatientData } from '../types';

interface PatientIdCardProps {
  patientData: PatientData;
}

const PatientIdCard: React.FC<PatientIdCardProps> = ({ patientData }) => {
  return (
    <div className="bg-gradient-to-br from-orange-50 to-yellow-50 w-[400px] h-[600px] relative border-4 border-orange-400 rounded-lg shadow-xl font-sans">
      {/* Orange corner decorations */}
      <div className="absolute top-0 left-0 w-16 h-1 bg-orange-400"></div>
      <div className="absolute top-0 right-0 w-16 h-1 bg-orange-400"></div>
      <div className="absolute bottom-0 left-0 w-16 h-1 bg-orange-400"></div>
      <div className="absolute bottom-0 right-0 w-16 h-1 bg-orange-400"></div>

      {/* Header Section */}
      <div className="p-4 text-center">
        {/* Hospital Logo and Name */}
        <div className="flex items-center justify-center mb-2">
          <div className="w-12 h-12 mr-3 flex-shrink-0">
            {/* Hospital Logo - Lotus design */}
            <svg viewBox="0 0 48 48" className="w-full h-full">
              <circle cx="24" cy="24" r="22" fill="#4F46E5" stroke="#312E81" strokeWidth="2"/>
              <path d="M24 8 L28 16 L36 12 L32 20 L40 24 L32 28 L36 36 L28 32 L24 40 L20 32 L12 36 L16 28 L8 24 L16 20 L12 12 L20 16 Z" fill="#FEF3C7"/>
              <circle cx="24" cy="24" r="6" fill="#F59E0B"/>
            </svg>
          </div>
          <div className="text-left">
            <h1 className="text-xl font-bold text-blue-800 tracking-wide">{patientData.hospitalName}</h1>
            <p className="text-sm text-blue-700 font-medium">{patientData.hospitalSubtitle}</p>
          </div>
        </div>

        {/* MRN/BH Card Title */}
        <div className="bg-blue-100 border border-blue-300 rounded px-4 py-1 inline-block">
          <h2 className="text-blue-800 font-bold text-sm underline">MRN/BH Card</h2>
        </div>
      </div>

      {/* Patient Information Section */}
      <div className="px-4 mb-4">
        <div className="bg-white border-2 border-gray-300 rounded-lg p-3">
          <div className="flex gap-4">
            {/* Patient Photo */}
            <div className="flex-shrink-0">
              <div className="w-20 h-24 bg-gradient-to-b from-sky-200 to-green-300 rounded border-2 border-blue-300 overflow-hidden relative">
                <img
                  src={patientData.photoUrl}
                  alt="Patient Photo"
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                  }}
                />
                {/* Landscape background when no photo */}
                <div className="absolute inset-0 bg-gradient-to-b from-sky-200 via-sky-100 to-green-300">
                  <div className="absolute bottom-0 left-0 right-0 h-8 bg-green-400 rounded-b"></div>
                  <div className="absolute top-4 left-2 w-3 h-3 bg-white rounded-full opacity-80"></div>
                  <div className="absolute top-6 right-3 w-2 h-2 bg-white rounded-full opacity-60"></div>
                </div>
              </div>
            </div>

            {/* Patient Details */}
            <div className="flex-1 space-y-1">
              <div>
                <span className="text-red-600 font-bold text-xs">MRN/BH ID No.: </span>
                <span className="text-black font-bold text-xs">{patientData.mrnBhId}</span>
              </div>
              <div>
                <span className="text-red-600 font-bold text-xs">Name: </span>
                <span className="text-black font-bold text-xs">{patientData.name}</span>
              </div>
              <div>
                <span className="text-red-600 font-bold text-xs">Age: </span>
                <span className="text-black font-bold text-xs">{patientData.age}</span>
              </div>
              <div>
                <span className="text-red-600 font-bold text-xs">Registered Mobile Number: </span>
                <span className="text-black font-bold text-xs">{patientData.registeredMobileNumber}</span>
              </div>

              {/* Barcode */}
              <div className="mt-2">
                <div className="flex gap-px">
                  {patientData.barcodeValue.split('').slice(0, 20).map((digit, index) => (
                    <div
                      key={index}
                      className="bg-black"
                      style={{
                        width: '2px',
                        height: parseInt(digit) % 2 === 0 ? '16px' : '12px'
                      }}
                    />
                  ))}
                </div>
                <p className="text-xs text-center mt-1 font-mono">{patientData.barcodeValue.slice(0, 20)}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Instructions for Patients */}
      <div className="px-4 mb-4">
        <div className="bg-white border-2 border-gray-300 rounded-lg p-3">
          <h3 className="text-blue-800 font-bold text-sm underline mb-2">Instructions for Patients</h3>
          <ul className="text-xs space-y-1">
            <li className="flex items-start">
              <span className="text-red-500 mr-1">•</span>
              <span>Please present this card at the billing desk for prompt service.</span>
            </li>
            <li className="flex items-start">
              <span className="text-red-500 mr-1">•</span>
              <span>Please use the MRN/BH number while booking the appointment.</span>
            </li>
            <li className="flex items-start">
              <span className="text-red-500 mr-1">•</span>
              <span>For appointment call on {patientData.appointmentPhoneNumber}</span>
            </li>
          </ul>
        </div>
      </div>

      {/* Patient Care Information */}
      <div className="px-4 mb-4">
        <div className="bg-blue-100 border border-blue-300 rounded px-4 py-1 text-center">
          <h3 className="text-blue-800 font-bold text-sm underline">Patient Care Information</h3>
        </div>

        <div className="mt-2 space-y-2">
          {/* Website and Appointment */}
          <div className="bg-white border border-gray-300 rounded p-2 flex items-center">
            <div className="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center mr-3">
              <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.559-.499-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.559.499.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.497-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.148.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z" clipRule="evenodd"/>
              </svg>
            </div>
            <div className="text-xs">
              <p><span className="text-blue-600 underline">Check out our Website: {patientData.websiteUrl}</span></p>
              <p className="text-red-600 font-bold">Book Doctor Appointment & Health Check Ups</p>
            </div>
          </div>

          {/* Social Media and Download */}
          <div className="flex gap-2">
            <div className="bg-white border border-gray-300 rounded p-2 flex-1">
              <p className="text-xs font-bold text-blue-800 mb-1">Follow Us On</p>
              <div className="flex gap-1">
                <div className="w-4 h-4 bg-blue-600 rounded"></div>
                <div className="w-4 h-4 bg-pink-500 rounded"></div>
                <div className="w-4 h-4 bg-blue-400 rounded"></div>
                <div className="w-4 h-4 bg-blue-700 rounded"></div>
                <div className="w-4 h-4 bg-red-600 rounded"></div>
              </div>
            </div>
            <div className="bg-white border border-gray-300 rounded p-2 flex-1">
              <div className="flex items-center">
                <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded mr-2"></div>
                <div className="text-xs">
                  <p className="text-blue-600 underline font-bold">Download Our</p>
                  <p className="text-blue-600 underline font-bold">Specialities Brochure</p>
                </div>
              </div>
            </div>
          </div>

          {/* Feedback and WhatsApp */}
          <div className="flex gap-2">
            <div className="bg-white border border-gray-300 rounded p-2 flex-1">
              <div className="flex items-center">
                <div className="w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded mr-2 flex items-center justify-center">
                  <span className="text-white text-xs">💝</span>
                </div>
                <div className="text-xs">
                  <p className="text-blue-600 underline font-bold">We Value Your</p>
                  <p className="text-blue-600 underline font-bold">Feedback</p>
                </div>
              </div>
            </div>
            <div className="bg-white border border-gray-300 rounded p-2 flex-1">
              <div className="flex items-center">
                <div className="w-6 h-6 bg-orange-500 rounded mr-2 flex items-center justify-center">
                  <span className="text-white text-xs">👨‍⚕️</span>
                </div>
                <div className="text-xs">
                  <p className="font-bold">Ask Bhakti</p>
                  <p className="text-green-600">on <span className="bg-green-500 text-white px-1 rounded">📱</span> {patientData.whatsappBhaktiNumber}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientIdCard;
